INSERT IGNORE INTO `tree` (`id`, `rootId`, `parentId`, `level`, `path`, `sort`, `last`, `public`, `uid`, `created`,
					`createdTime`, `createdTimeOrder`, `edited`, `editedTime`, `authorRId`, `template`, `type`,
					`publicFrom`, `publicTo`, `name`, `nameTitle`, `nameAnchor`, `annotation`, `description`,
					`keywords`, `content`, `hideFirstImage`, `links`, `seoTitleFilter`, `seoAnnotationFilter`,
					`seoDescriptionFilter`, `videos`, `hideCrossroad`, `hideInSitemap`, `forceNoIndex`, `hideInSearch`,
					`showContactForm`, `showOnHomepage`, `customFieldsJson`, `customContentJson`,
					`customContentSchemeJson`, `productAttachedId`, `hasLinkedCategories`)
VALUES (550, 1, 72, 2, '1|72|', 9, 1, 1, 'userOrderedProducts', 0, '2025-06-03 11:47:17', '2025-06-03 11:47:17', 40,
		'2025-06-03 11:48:31', NULL, 'User:default', 'common', '2025-06-03 11:47:17', '2125-06-03 11:47:17',
		'Zakoupené produkty', 'Zakoupené produkty', 'Zakoupené produkty', '', '', '', '', 0, '', NULL, NULL, NULL, '',
		0, 0, 0, 0, 0, 0, '{}', '{}', '[]', NULL, NULL);

UPDATE `tree` SET `customFieldsJson` = '{\"userMenuUnloggedUser\":[{\"tree\":37},{\"tree\":76},{\"tree\":74}],\"userMenuLoggedUser\":[{\"tree\":75},{\"tree\":413},{\"tree\":36},{\"tree\":550},{\"tree\":29},{\"tree\":405}],\"userSideMenu\":[{\"tree\":72},{\"tree\":75},{\"tree\":413},{\"tree\":36},{\"tree\":\"550\"},{\"tree\":29},{\"tree\":405}]}' WHERE `id` = '72';
