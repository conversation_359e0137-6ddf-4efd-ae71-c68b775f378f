<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ProductDataGrid;

use App\Model\ElasticSearch\ClientFactory;
use App\Model\Orm;
use App\Model\Parameter;
use Elasticsearch\ClientBuilder;
use Nette\Application\UI\Control;
use SuperKoderi\MutationsHolder;
use SuperKoderi\Translator;
use Ublaboo\DataGrid\DataGrid;
use Ublaboo\DataGrid\DataSource\ElasticsearchDataSource;
use Ublaboo\DataGrid\Exception\DataGridException;
use Ublaboo\DataGrid\Filter\FilterDateRange;
use Ublaboo\DataGrid\Filter\FilterSelect;

class ProductDataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly ClientFactory $clientFactory,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/productDataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): DataGrid
	{

		$csMutation = $this->mutationsHolder->getDefault();
		$esIndex = $this->orm->esIndex->getAllLastActive($csMutation);

		$client = $this->clientFactory->create();

		$dataSource = new ElasticsearchDataSource(
			$client, // Elasticsearch\Client
			$esIndex->name, // Index name
		);


		$grid = new DataGrid();

		$baseFilter = new FilterSelect($grid, 'type', 'type', [
			'type' => 'product'
		], 'type');
		$baseFilter = $baseFilter->setValue('product');
		$dataSource->applyFilterSelect($baseFilter);

		$grid->setDataSource($dataSource);
		$grid->setItemsPerPageList([20, 50], false);



		$grid->addColumnText('id', 'id')->setFilterText()->setExactSearch();
		$grid->addColumnText('intCislo', 'ublaboo_intCislo', 'filter.intCislo')
			->setRenderer(function ($data) {
				return (isset($data['filter']['intCislo']) ? $data['filter']['intCislo'] : '');
			})
			->setFilterText()->setExactSearch()

		;
		$grid->addColumnText('name', 'name')->setFilterText();


		$parameter = $this->orm->parameter->getBy(['uid' => 'year']);
		if ($parameter !== null) {
			$grid->addColumnText('year','ublaboo_year', 'filter.year')
				->setRenderer(function ($data) {
					return (isset($data['filter']['year']) ? $data['filter']['year'] : '');
				})
				->setFilterSelect($this->getOptions($parameter))
				->setPrompt('Vše')
			;
		}

		$parameter = $this->orm->parameter->getBy(['uid' => 'series']);
		if ($parameter !== null) {
			$grid->addColumnText('series','ublaboo_series', 'filter.series')
				->setRenderer(function ($data) {
					return (isset($data['filter']['series']) ? $data['filter']['series'] : '');
				})
				->setFilterSelect($this->getOptions($parameter))
				->setPrompt('Vše')
			;
		}


		$parameter = $this->orm->parameter->getBy(['uid' => 'batchNumber']);
		if ($parameter !== null) {
			$grid->addColumnText('batchNumber','ublaboo_batchNumber', 'filter.batchNumber')
				->setRenderer(function ($data) {
					return (isset($data['filter']['batchNumber']) ? $data['filter']['batchNumber'] : '');
				})
				->setFilterText()
			;
		}

		$grid->addColumnText('isSet','ublaboo_isSet', 'filter.isSet')
			->setRenderer(function ($data) {
				return ((isset($data['filter']['isSet']) && $data['filter']['isSet']) ?  'Set' : '');
			})
			->setFilterSelect([
				0 => 'Není set',
				1 => 'Je set',
			])->setPrompt('Vše')
		;

		$grid->addColumnText('isInStock','ublaboo_isInStock', 'filter.isInStock')
			->setRenderer(function ($data) {
				return ((isset($data['filter']['isInStock']) && $data['filter']['isInStock']) ?  'Skladem' : 'Vyprodáno');
			})
			->setFilterSelect([
				1 => 'Skladem',
				0 => 'Vyprodáno',
			])->setPrompt('Vše')
		;

		$grid->addColumnText('isNew','ublaboo_isNew', 'filter.isNew')
			->setRenderer(function ($data) {
				return ((isset($data['filter']['isNew']) && $data['filter']['isNew']) ?  'Novinka' : '');
			})
			->setFilterSelect([
				1 => 'Novinky',
				0 => 'Staré',
			])->setPrompt('Vše')
		;

		$grid->addColumnText('isAction','ublaboo_isAction', 'filter.isAction')
			->setRenderer(function ($data) {
				return ((isset($data['filter']['isAction']) && $data['filter']['isAction']) ?  'Akce' : '');
			})
			->setFilterSelect([
				1 => 'V akci',
				0 => 'Bez akce',
			])->setPrompt('Vše')
		;

		$grid->addColumnText('isSale','ublaboo_isSale', 'filter.isSale')
			->setRenderer(function ($data) {
				return ((isset($data['filter']['isSale']) && $data['filter']['isSale']) ?  'Výprodej' : '');
			})
			->setFilterSelect([
				1 => 'Ve výprodeji',
				0 => 'Bez výprodeje',
			])->setPrompt('Vše')
		;

		$grid->addColumnText('isIconic','ublaboo_isIconic', 'filter.isIconic')
			->setRenderer(function ($data) {
				return ((isset($data['filter']['isIconic']) && $data['filter']['isIconic']) ?  'Ikonické' : '');
			})
			->setFilterSelect([
				1 => 'Ikonické',
				0 => 'Běžné',
			])->setPrompt('Vše')
		;


		$grid->addColumnDateTime('public', 'public', 'filter.public')
			->setRenderer(function ($data) {
				return '';
			})
			->setFilterDateRange('filter.public')
		;

		$grid->addAction('edit', 'Edit', 'Product:edit')->setClass('btn btn-xs btn-primary');
		$grid->setTranslator($this->translator);


		return $grid;
	}

	private function getOptions(Parameter $parameter): array
	{
		$options = [];
		foreach ($parameter->options as $option) {
			$options[(string)$option->internalValue] = (string)$option->internalValue;
		}

		return $options;
	}

}
