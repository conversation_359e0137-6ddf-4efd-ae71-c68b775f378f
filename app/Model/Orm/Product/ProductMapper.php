<?php


namespace App\Model;


use App\Tratis\Orm\hasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Dbal\Result\Row;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class ProductMapper extends DbalMapper
{
	use hasCamelCase;

	protected $tableName = 'product';

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions); // property is not available on interface
		$conventions->setMapping('linksString', 'links');
		$conventions->setMapping('videosString', 'videos');

		$conventions->manyHasManyStorageNamePattern = '%s_%s';

		return $conventions;
	}


	public function getManyHasManyParameters(\Nextras\Orm\Entity\Reflection\PropertyMetadata $sourceProperty, \Nextras\Orm\Mapper\Dbal\DbalMapper $targetMapper): array
	{
		if ($targetMapper instanceof ParameterValueMapper) {
			return ['product_parameter', ['productId', 'parameterValueId']];
		}

		return parent::getManyHasManyParameters($sourceProperty, $targetMapper);
	}



	public function findPromoted(): ICollection
	{
		$builder = $this->builder()->select('p.*')->from('product', 'p')
			->orderBy('RAND()')
			->limitBy(4);
		return $this->toCollection($builder);
	}


	public function findRandom(): ICollection
	{
		$builder = $this->builder();
		$builder->orderBy('RAND()');
		return $this->toCollection($builder);
	}


	public function findByFilter(mixed $filter): ICollection
	{
//		$builder = $this->builder();
		$builder = $this->builder()->select('p.*')
			->from('product', 'p');
		$builder->joinInner('[product_variant] as pv', '[p.id] = [pv.productId]');


		if (!empty($filter->from)) {
			$builder->andWhere('priceDPH >= %s', $filter->from);
		}
		if (!empty($filter->to)) {
			$builder->andWhere('priceDPH <= %s', $filter->to);
		}

		if (!empty($filter->fulltext)) {
			$builder->andWhere('( p.name LIKE %_like_ OR pv.code LIKE %like_ OR pv.ean LIKE %like_)', $filter->fulltext, $filter->fulltext, $filter->fulltext);
		}

		if (!empty($filter->isNew)) {
			$builder->andWhere('isNew = %b', $filter->isNew);
		}

		if (!empty($filter->isAction)) {
			$builder->andWhere('isAction = %b', $filter->isAction);
		}

		if (!empty($filter->isPublic)) {
			$builder->andWhere('public = %b', $filter->isPublic);
		}


		$builder->groupBy('p.id');

		return $this->toCollection($builder);
	}


	// PRODUKTOVE SETY
	public function findBySetpartsById(int $id): ICollection
	{
		$builder = $this->builder()->select('p.*')->from('product', 'p')
			->joinInner('[product_set] as ps', '[p.id] = [ps.productId] AND [ps.productMainId] = %i',
			$id)->orderBy('ps.sort');
		return $this->toCollection($builder);
	}


	public function addToSet(Product $set, Product $product, int $sort): Result
	{
		return $this->connection->query('INSERT INTO product_set %values', [
			'productMainId' => $set->id,
			'productId' => $product->id,
			'sort' => $sort
		]);
	}

	public function findOrderedProductsByUser(User $user): ICollection
	{
		$builder = $this->builder()->select('p.*')->from('product', 'p')
			->joinInner('[order_item] as oi', '[p.id] = [oi.productId]')
			->joinInner('[order] as o', '[oi.orderId] = [o.id]')
			->andWhere('o.userId = %i', $user->id)
			->andWhere('o.status = %s', 'done');
		return $this->toCollection($builder);
	}


	public function removeFromSet(Product $set, Product $product): Result
	{
		$ret = $this->connection->query('DELETE FROM product_set WHERE productMainId = %i AND productId = %i ', $set->id, $product->id);
		//$this->getRepository()->persistAndFlush($set);
		return $ret;
	}


	public function findByParameterValuesUnion(Discount $discount): ICollection
	{
		$parameterValuesIds = $discount->parametersValues->toCollection()->fetchPairs(null, 'id');

		if ($parameterValuesIds !== []) {

			$builder = $this->builder();
			$builder->select('p.*')
				->from($this->tableName, 'p')
				->joinLeft('[product_parameter] as pp', '[p.id] = [pp.productId]');

			$builder = $builder->andWhere('parameterValueId in %i[]', $parameterValuesIds);
			$builder = $builder->groupBy('pp.productId');

			$builder->having('count(pp.productId) = %i', count($parameterValuesIds));
			return $this->toCollection($builder);
		} else {
			return new EmptyCollection();
		}
	}

	public function findByParameterValue(string $parameterUid, string $value): ICollection
	{
		$builder = $this->builder();
		$builder->select('product.*')
			->from("product_parameter")
			->joinLeft('parameter_value', 'parameter_value.id = product_parameter.parameterValueId')
			->joinLeft('product', 'product.id = product_parameter.productId')
			->joinLeft('parameter', 'parameter.id = product_parameter.parameterId');

		$builder->andWhere('parameter_value.internalValue = %s', $value);
		$builder->andWhere('parameter.uid = %s', $parameterUid);

		return $this->toCollection($builder);
	}


	public function findSetBySetpartId(int $id): ICollection
	{
		$builder = $this->builder()->select('p.*')->from('product', 'p')
			->joinInner( '[product_set] as ps', '[p.id] = [ps.productMainId] AND [ps.productId] = %i',
			$id);
		return  $this->toCollection($builder);
	}




	/**
	 * @return ICollection|Product[]
	 */
	public function searchByName(string $q, array $excluded = NULL, Mutation $mutation = null): ICollection
	{
		$builder = $this->builder()->select('p.*')->from($this->tableName, 'p');

		if ($mutation) {
			$builder->joinInner('[product_localization] as pl', 'p.id = pl.productId and pl.mutationId = %i', $mutation->id);
		} else {
			$builder->andWhere('internalName LIKE %_like_', $q);
		}


//		if ($sourceId) {
//			$builder->andWhere('id != %i', $sourceId); // nenabizet ten stejny produkt
//		}

//		$excluded =  [1, 3, 34];
		if ($excluded) {
			$builder->andWhere('id NOT IN %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}


	public function findMainProductsInRelations(Product $attachedProduct, string $type): ICollection
	{
		$builder = $this->builder();
		$builder->joinInner( '[product_product] as pp', '[product.id] = [pp.mainProductId] AND [pp.type] = %s', $type);
		$builder->andWhere('pp.attachedProductId = %i', $attachedProduct->id);

		$builder->orderBy('pp.sort');

		return $this->toCollection($builder);
	}


	public function findAttachedProductsInRelations(Product $mainProduct, string $type): ICollection
	{
		$builder = $this->builder();
		$builder->joinInner( '[product_product] as pp', '[product.id] = [pp.attachedProductId] AND [pp.type] = %s', $type);
		$builder->andWhere('pp.mainProductId = %i', $mainProduct->id);
		$builder->orderBy('pp.sort');

		return $this->toCollection($builder);
	}




	public function findProductsInTreeProductRelations(Tree $tree, string $type): ICollection
	{
		$builder = $this->builder();
		$builder->joinInner( '[tree_product] as tp', '[product.id] = [tp.productId] AND [tp.type] = %s', $type);
		$builder->andWhere('tp.treeId = %i', $tree->id);
		$builder->orderBy('tp.sort');
		return $this->toCollection($builder);
	}


	public function addParameterValue(Product|Row $product, ParameterValue $parameterValue): Result
	{
		$values = [
			'productId' => $product->id,
			'parameterId' => $parameterValue->parameter->id,
			'parameterValueId' => $parameterValue->id
		];

		return $this->connection->query('INSERT INTO product_parameter %values ON DUPLICATE KEY UPDATE %set', $values, $values);
	}



	public function removeParameterValue(Product|Row $product, ParameterValue $parameterValue): Result
	{
		$ret = $this->connection->query('DELETE FROM product_parameter WHERE productId = %i AND parameterValueId = %i LIMIT 1', $product->id, $parameterValue->id);
		return $ret;
	}

	public function removeParameter(Product $product, Parameter $parameter): Result
	{
		$ret = $this->connection->query('DELETE FROM product_parameter WHERE productId = %i AND parameterId = %i LIMIT 1', $product->id, $parameter->id);
		return $ret;
	}

	public function removeMissingParameterValuesIds(Product $product, array $selectedParameterValuesIds = []): Result
	{
		if ($selectedParameterValuesIds) {
			$ret = $this->connection->query('DELETE FROM product_parameter WHERE productId = %i AND parameterValueId not in %i[]', $product->id, $selectedParameterValuesIds);
		} else {
			$ret = $this->connection->query('DELETE FROM product_parameter WHERE productId = %i', $product->id);
		}
		return $ret;
	}

    public function addToPages(Tree $page, Product $product, int $sort): Result
	{
		return $this->connection->query('INSERT INTO product_page %values', [
			'treeId' => $page->id,
			'productId' => $product->id,
			'sort' => $sort
		]);
	}


	public function removeFromPages(Tree $page, Product $product): Result
	{
		$ret = $this->connection->query('DELETE FROM product_page WHERE treeId = %i AND productId = %i LIMIT 1', $page->id, $product->id);
		//$this->getRepository()->persistAndFlush($set);
		return $ret;
	}


	public function updateToPages(Tree $page, Product $product, int $sort): Result
	{
		return $this->connection->query('UPDATE product_page SET sort=%s  WHERE treeId = %i AND productId = %i LIMIT 1',
			$sort, $page->id, $product->id);
	}

	public function findSimilarProducts(Product $product, int $count, array $excludedIds = [], array $parameterUIDs = []) : ICollection
	{
//		Pokud ani tak nebudou 4 tak načíst random další auta, vždy musí být 4 auta

		// category
		$catIds = [];
		foreach ($product->productTrees as $t) {
			if ($t->tree->id != 1 && $t->tree->id != 21) { // TODO refactor
				$catIds[] = $t->tree->id;
			}
		}

		$query = $this->builder();
		$query->select('p.*')->from($this->tableName, 'p');

		// dotaz na pocet kusu skladem
		$query->joinInner( '[product_variant] as pv', '[p.id] = [pv.productId]');

		if (count($catIds)) {
			$query->joinInner( '[product_tree] as pp', '[p.id] = [pp.productId] AND [pp.treeId] IN %i[]',
				$catIds);
		}

		// parametry
		/*$counter = 0;
		foreach ($product->parametersValues as $parameterValue) {
			$counter++;
			//dump([$parameterValue->parameter->uid, $parameterValue->parameter->id, $parameterValue->value]);
			if (in_array($parameterValue->parameter->uid, $parameterUIDs)) {
				$alias = 'pp' . $parameterValue->parameter->id.'_'.$counter; // fix - for one or more valies for same parameter
				$query->innerJoin('p', '[product_parameter]', $alias, '[p.id] = [' . $alias . '.productId] AND ' . $alias . '.parameterId = %i AND ' . $alias . '.parameterValueId = %i',
					$parameterValue->parameter->id, $parameterValue->id);
			}
		}*/

		$query->andWhere('p.id!=%i', $product->id)
			->andWhere('p.public=%i', 1)
			->orderBy('RAND()')
			->groupBy('p.id')
			->limitBy($count);

//		$query->andWhere('p.priceDPH BETWEEN %i AND %i', (int)round($product->priceDPH * 0.4), (int)round($product->priceDPH * 1.2));
//			->andWhere('p.model = %s', $product->model)
//			->andWhere('p.manufacturer = %s', $product->manufacturer);

		if ($excludedIds !== []) {
			$query->andWhere('p.id not in %i[]', $excludedIds);
		}

		return $this->toCollection($query);
	}


	public function findFilteredProducts(array $productIds) : ICollection
	{
		$builder = $this->builder()->select('p.*')->from('product', 'p')
			->andWhere('p.id in %i[]', $productIds)
			->orderBy('%raw', 'FIELD(p.id, '.implode(',', $productIds).')');
		return  $this->toCollection($builder);
	}

	/**
	 * @param Tree|null $category
	 * @return ICollection|Product[]
	 */
	public function findBestseller(Mutation $mutation, Tree $category = null) : ICollection
	{
		$builder = $this->builder()->select('p.*')->from('product', 'p');

		$builder->joinInner('[product_localization] as pl', '[p.id] = [pl.productId] AND ([pl.mutationId] = %i)', $mutation->id);
		$builder->joinInner('[product_variant] as pv', '[p.id] = [pv.productId]');
		$builder->joinInner('[product_variant_localization] as pvl', '[pv.id] = [pvl.variantId] AND ([pvl.mutationId] = %i)', $mutation->id);

		if ($category) {
			$builder->joinInner( '[product_tree] as pt', '[p.id] = [pt.productId] AND [pt.treeId] = %i', $category->id);
		}

		$builder->andWhere('pl.public = %i', 1);
		$builder->andWhere('pvl.active = %i', 1);
		$builder->orderBy('p.soldCount desc');
		return $this->toCollection($builder);
	}


	public function disableIsNewFlag(int $productId): void
	{
		$this->connection->query('UPDATE product SET isNew = 0 where id = %i', $productId);
	}



	protected function getBySarze(string $sarze): void
	{
		// todo
	}


	public function findAllIds(?int $limit): Result
	{
		$builder = $this->builder()->select('p.id')
			->from('product', 'p')
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}


	public function resetSoldCount(): void
	{
		$this->connection->query("UPDATE product SET soldCount = 0");
	}

}
