<?php

namespace FrontModule;

use App\Components\VisualPaginator\VisualPaginator;
use App\Model\Order;
use App\Model\RoutableEntity;
use App\Model\Tree;
use App\Model\UserHash;
use App\Model\UserHashModel;
use Nette\Application\AbortException;
use Nette\Application\UI\InvalidLinkException;
use SuperKoderi\Components\ChangePasswordForm;
use SuperKoderi\Components\FavoriteProducts;
use SuperKoderi\Components\IChangePasswordFormFactory;
use SuperKoderi\Components\IFavoriteProductsFactory;
use SuperKoderi\Components\ILostPasswordFormFactory;
use SuperKoderi\Components\IMessageForFormFactory;
use SuperKoderi\Components\IOrderHistoryDetailFactory;
use SuperKoderi\Components\IOrderHistoryFactory;
use SuperKoderi\Components\IProfileFormFactory;
use SuperKoderi\Components\IRegistrationFormFactory;
use SuperKoderi\Components\IUserSideMenuFactory;
use SuperKoderi\Components\LostPasswordForm;
use SuperKoderi\Components\MessageForForm;
use SuperKoderi\Components\OrderHistory;
use SuperKoderi\Components\OrderHistoryDetail;
use SuperKoderi\Components\ProfileForm;
use SuperKoderi\Components\RegistrationForm;
use SuperKoderi\Components\UserSideMenu;

/**
 * @property Tree $object
 * @method Tree getObject()
 */
class UserPresenter extends BasePresenter
{
	/** @persistent */
	public string $backlink = '';

	public function __construct(
		private readonly IProfileFormFactory $profileFormFactory,
		private readonly IChangePasswordFormFactory $changePasswordFormFactory,
		private readonly ILostPasswordFormFactory $lostPasswordFormFactory,
		private readonly IMessageForFormFactory $messageForFormFactory,
		private readonly IRegistrationFormFactory $registrationFormFactory,
		private readonly IOrderHistoryFactory $orderHistoryFactory,
		private readonly IOrderHistoryDetailFactory $orderHistoryDetailFactory,
		private readonly IUserSideMenuFactory $userSideMenuFactory,
		private readonly IFavoriteProductsFactory $favoriteProductsFactory,
		private readonly UserHashModel $userHashModel
	) {
		parent::__construct();
	}

	public ?Order $order = null;

	protected function startup(): void
	{
		parent::startup();
		$this->setObject($this->orm->tree->getById($this->params['idref']));
	}


	public function actionDefault(): void
	{
		if ($this->user->loggedIn) {
			if ($this->isAjax()) {
				$this->redrawControl();
			}

		} else {
			$this->redirectToLoginPage();
		}
	}


	public function actionLogin(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}


	public function actionLostPassword(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}
	}


	public function actionProfil(): void
	{
		if (!$this->user->loggedIn) {
			$this->redirectToLoginPage();
		}
	}


	public function actionOrderHistory(string $order = null): void
	{
		if (!$this->user->loggedIn) {
			$this->redirectToLoginPage();
		}

		if ($order && $this->userEntity) {
			$this->order = $this->orm->order->getBy(
			[
				'hash' => $order,
				'user' => $this->userEntity,
			]
			);
		}

		$this->template->order = $this->order;
	}

	public function actionFavorite(): void
	{
		if (!$this->user->loggedIn) {
			$this->redirectToLoginPage();
		}
	}

	public function actionRegistration(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}
	}


	public function actionResetPassword(?string $hashToken): void
	{
		if ($hashToken) {

			$userHash = $this->userHashModel->getHash($hashToken, UserHash::TYPE_LOST_PASSWORD);
			if (!$userHash || !$userHash->isValid()) {
				$this->getComponent('lostPasswordForm')?->flashMessage("reset_password_expired_link", "error");
				$this->redirect($this->mutation->pages->lostPassword);
			}

		} else {
			$this->getComponent('lostPasswordForm')?->flashMessage("reset_password_no_valid_link", "error");
			$this->redirect($this->mutation->pages->lostPassword);
		}

	}

	public function handleLogout(): void
	{
		$this->getUser()->logout(TRUE);
		$this->flashMessage('msg_info_logout');
		$this->redirectToUserPage('title');
	}


	public function createComponentVp(): VisualPaginator
	{
		return $this->visualPaginatorFactory->create();
	}


	public function handleRepeatOrder(int $orderId): void
	{
		$order = $this->orm->order->getById($orderId);

		if ($order->user->id == $this->userEntity->id) {
			foreach ($order->products as $orderItem) {
				$this->basket->addProductVariant($orderItem->variant, $orderItem->amount);
			}
		}
		$this->redirect($this->mutation->pages->step1);
	}


	/**
	 * presmerovani na prihlasovaci stranku, v pripade pristupu do zabezpecene sekce
	 */
	private function redirectToLoginPage(): never
	{
		$this->redirectToUserPage('userLogin');
	}


	/**
	 * presmerovani na detail uziv. sekce, pri pristupu na registraci, zapomenute heslo a prihlaseni
	 */
	private function redirectToProfilePage(): never
	{
		$this->redirectToUserPage('userProfil');
	}


	/**
	 * @param string $uid
	 * @throws AbortException
	 * @throws InvalidLinkException
	 */
	private function redirectToUserPage(string $uid): never
	{
		// kdyz je ajax a fancybox=true - JS presmerovani
		if ($this->isAjax()) {
			if (isset($_GET['fancybox'])) {
//				$this->presenter->setLayout(false);
//				$this->setView("extra");
				$url = $this->link($this->mutation->pages->$uid);
//				echo '<meta http-equiv="refresh" content="0; url='.$url.'" />';
				echo '<script>
                            window.location = "' . $url . '"
						</script>';

				$this->terminate();
			}
		}
		//, array('backlink' => $this->storeRequest())
		$this->redirect($this->mutation->pages->$uid);
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}


	protected function createComponentUserSideMenu(): UserSideMenu
	{
		assert($this->object instanceof RoutableEntity);
		return $this->userSideMenuFactory->create($this->object);
	}


	public function createComponentRegistrationForm(): RegistrationForm
	{
		return $this->registrationFormFactory->create($this->object);
	}


	public function createComponentOrderHistory(): OrderHistory
	{
		return $this->orderHistoryFactory->create($this->object, $this->userEntity);
	}


	public function createComponentOrderHistoryDetail(): OrderHistoryDetail
	{
		return $this->orderHistoryDetailFactory->create($this->object, $this->userEntity, $this->order);
	}


	public function createComponentProfileForm(): ProfileForm
	{
		return $this->profileFormFactory->create($this->object, $this->userEntity);
	}


	public function createComponentChangePasswordForm(): ChangePasswordForm
	{
		return $this->changePasswordFormFactory->create($this->object, $this->userEntity);
	}

	public function createComponentFavoriteProducts(): FavoriteProducts
	{
		return $this->favoriteProductsFactory->create($this->object, $this->userEntity, $this->mutation, $this->priceLevel, $this->currentState);
	}


	public function createComponentLostPasswordForm(): LostPasswordForm
	{
		$hash = NULL;
		$params = $this->getParameters();
		if (isset($params['hashToken'])) {
			$hash = $params['hashToken'];
		}
		return $this->lostPasswordFormFactory->create($this->object, $hash);
	}

}
