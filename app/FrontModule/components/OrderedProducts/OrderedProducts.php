<?php declare(strict_types=1);

namespace SuperKoderi\Components;

use App\Model\Orm;
use App\Model\Tree;
use App\Model\User;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
class OrderedProducts extends Control
{
	public function __construct(
		private readonly Tree $object,
		private readonly User $user,
		private readonly Orm $orm,
	)
	{
	}

	public function render()
	{
		$this->template->setTranslator($this->presenter->getTranslator());
		$this->template->object = $this->object;
		$this->template->userEntity = $this->user;

		$this->template->products = $this->orm->product->findOrderedProductsByUser($this->user);

		$this->template->render(__DIR__ . '/orderedProducts.latte');
	}
}
